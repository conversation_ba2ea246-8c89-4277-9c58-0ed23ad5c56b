limits-3.7.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
limits-3.7.0.dist-info/LICENSE.txt,sha256=T6i7kq7F5gIPfcno9FCxU5Hcwm22Bjq0uHZV3ElcjsQ,1061
limits-3.7.0.dist-info/METADATA,sha256=EUabf_Y_kURN4qtkyeguZu_rlNI38lQUB1PgKPEvv1s,7314
limits-3.7.0.dist-info/RECORD,,
limits-3.7.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
limits-3.7.0.dist-info/WHEEL,sha256=Xo9-1PvkuimrydujYJAjF7pCkriuXBpUPEjma1nZyJ0,92
limits-3.7.0.dist-info/top_level.txt,sha256=C7g5ahldPoU2s6iWTaJayUrbGmPK1d6e9t5Nn0vQ2jM,7
limits/__init__.py,sha256=j_yVhgN9pdz8o5rQjVwdJTBSq8F-CTzof9kkiYgjRbw,728
limits/__pycache__/__init__.cpython-312.pyc,,
limits/__pycache__/_version.cpython-312.pyc,,
limits/__pycache__/errors.cpython-312.pyc,,
limits/__pycache__/limits.cpython-312.pyc,,
limits/__pycache__/strategies.cpython-312.pyc,,
limits/__pycache__/typing.cpython-312.pyc,,
limits/__pycache__/util.cpython-312.pyc,,
limits/__pycache__/version.cpython-312.pyc,,
limits/_version.py,sha256=-BJDgGzH-CbJma8-Y_Y0s314aB1Y3YzjezQxt_8tfDw,497
limits/aio/__init__.py,sha256=IOetunwQy1c5GefzitK8lewbTzHGiE-kmE9NlqSdr3U,82
limits/aio/__pycache__/__init__.cpython-312.pyc,,
limits/aio/__pycache__/strategies.cpython-312.pyc,,
limits/aio/storage/__init__.py,sha256=CbtuSlVl1jPyN_vsEI_ApWblDblVaL46xcZ2M_oM0V8,595
limits/aio/storage/__pycache__/__init__.cpython-312.pyc,,
limits/aio/storage/__pycache__/base.cpython-312.pyc,,
limits/aio/storage/__pycache__/etcd.cpython-312.pyc,,
limits/aio/storage/__pycache__/memcached.cpython-312.pyc,,
limits/aio/storage/__pycache__/memory.cpython-312.pyc,,
limits/aio/storage/__pycache__/mongodb.cpython-312.pyc,,
limits/aio/storage/__pycache__/redis.cpython-312.pyc,,
limits/aio/storage/base.py,sha256=gsy_91lvm53AoAKi1plaegcEOSZXK3OiMP0mxA6lPAw,2941
limits/aio/storage/etcd.py,sha256=iGGuKHN5cCW9r4nLeiZ4TnoU1pfSXXLZhS2YOWznryw,4568
limits/aio/storage/memcached.py,sha256=aR7f5NY-mXtFFjphbaxoL7z1cAHZuRYjpGUVOktoI4A,4296
limits/aio/storage/memory.py,sha256=A506i0vncBmsaL9GGAqepQkPAXyc-HxmJ8gS8kJtqpE,5597
limits/aio/storage/mongodb.py,sha256=xnZTyogdLdYir5xQMs_yQTBJt6B5P1pJx0VYBAo_Ndc,9041
limits/aio/storage/redis.py,sha256=k0YgeXlvITUZEjql32KNm1vhN73zZHCcx0QJyK_Gg94,15080
limits/aio/strategies.py,sha256=REaQ-lqgqkN5wrFZ26AZ3sCHO8oZBL_mWhI6nMRaBz8,6485
limits/errors.py,sha256=sUolBUfTFLQSzo6dfE2E9j_0K7_8Nr9_Hx-v5C4D0EU,416
limits/limits.py,sha256=lwQnA5wegkW_AXtplOH3tLuQ1LByMX9hqHJquYVYdTs,4943
limits/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
limits/resources/redis/lua_scripts/acquire_moving_window.lua,sha256=5CFJX7D6T6RG5SFr6eVZ6zepmI1EkGWmKeVEO4QNrWo,483
limits/resources/redis/lua_scripts/clear_keys.lua,sha256=zU0cVfLGmapRQF9x9u0GclapM_IB2pJLszNzVQ1QRK4,184
limits/resources/redis/lua_scripts/incr_expire.lua,sha256=Uq9NcrrcDI-F87TDAJexoSJn2SDgeXIUEYozCp9S3oA,195
limits/resources/redis/lua_scripts/moving_window.lua,sha256=ir0SkuRVnrqkVSFNIuedTV_KW6zG70Z56u6-_FpR_20,352
limits/storage/__init__.py,sha256=8i1-SoTEV_XGAMYDepcLra7do-Tx4rUPbPrUQVVJgTw,2518
limits/storage/__pycache__/__init__.cpython-312.pyc,,
limits/storage/__pycache__/base.cpython-312.pyc,,
limits/storage/__pycache__/etcd.cpython-312.pyc,,
limits/storage/__pycache__/memcached.cpython-312.pyc,,
limits/storage/__pycache__/memory.cpython-312.pyc,,
limits/storage/__pycache__/mongodb.cpython-312.pyc,,
limits/storage/__pycache__/redis.cpython-312.pyc,,
limits/storage/__pycache__/redis_cluster.cpython-312.pyc,,
limits/storage/__pycache__/redis_sentinel.cpython-312.pyc,,
limits/storage/__pycache__/registry.cpython-312.pyc,,
limits/storage/base.py,sha256=-JV-zAkss7pOETZyPYjo8ZZqTMGs-DPqgz_gcfArXfs,2818
limits/storage/etcd.py,sha256=SlDHRItliRaR2j5Rf_v_oQ57ITpmNMYQJSamM3SbwCA,4258
limits/storage/memcached.py,sha256=qMvRIEtRRzZXSgcZSUTBgUlBDeNOpIr_gDTV2r5SPak,6005
limits/storage/memory.py,sha256=plPsyLB26QN9I6jQq00fra-cdh8xoDAgaoM-kWuECtk,5302
limits/storage/mongodb.py,sha256=Z4_Og2Ys6uwQT_D4dUDdDAEndBY7UfCh_LiHgZf8IUk,7909
limits/storage/redis.py,sha256=-5_kQuvC6tjMUgDoYAsANqMouu0PujE-jocRqmxXvsc,7970
limits/storage/redis_cluster.py,sha256=fINAmdZSs_0lAr3p_TdZtpWAUqmiWmKFNKyfRcMkSTQ,5378
limits/storage/redis_sentinel.py,sha256=AxbtYZQXCNL-4yu7dQ8i6MhwO2PNLMRLHe3JcHkMKvA,3647
limits/storage/registry.py,sha256=xcBcxuu6srqmoS4WqDpkCXnRLB19ctH98v21P8S9kS8,708
limits/strategies.py,sha256=7pr2V34KdOEfxnYOf882Cl2qKY-KK6HwKjdYo_IsD4c,6690
limits/typing.py,sha256=h6K8fbmgCyJih2qJFq52_6Ic1WmFNDQx1UbdEGHCD5o,2976
limits/util.py,sha256=LRsn6i3KmQE33Ea4FTf4IxD9SMtwiKVXN5iDxTEuOuc,5743
limits/version.py,sha256=YwkF3dtq1KGzvmL3iVGctA8NNtGlK_0arrzZkZGVjUs,47
