
# This file was generated by 'versioneer.py' (0.22) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-11-24T14:02:38-0800",
 "dirty": false,
 "error": null,
 "full-revisionid": "0946fdcd3b9509041a823d3bb1b63ea1de9ab6ee",
 "version": "3.7.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
