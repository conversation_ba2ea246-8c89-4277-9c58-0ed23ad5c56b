from cryptography.fernet import Fernet
from app.config import settings
import base64
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class Encryptor:
    def __init__(self):
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=settings.SECRET_KEY.encode(),
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(settings.AES_KEY.encode()))
        self.fernet = Fernet(key)

    def encrypt(self, data: str) -> str:
        if not data:
            return data
        return self.fernet.encrypt(data.encode()).decode()

    def decrypt(self, encrypted_data: str) -> str:
        if not encrypted_data:
            return encrypted_data
        return self.fernet.decrypt(encrypted_data.encode()).decode()

encryptor = Encryptor() 