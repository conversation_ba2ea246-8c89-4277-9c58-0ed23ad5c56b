from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Enum, or_
from sqlalchemy.orm import relationship
from app.database import db
import enum
from app.utils.encryption import encryptor
from app.models.user import User

class AppointmentStatus(enum.Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    COMPLETED = "completed"

class Appointment(db.Model):
    __tablename__ = "appointments"
    
    id = Column(Integer, primary_key=True)
    visitor_name = Column(String(100), nullable=False)
    visitor_contact = Column(String(255), nullable=False)  # Encrypted
    vehicle_no = Column(String(20))  # Encrypted, nullable
    purpose = Column(String(500), nullable=False)
    host_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    status = Column(Enum(AppointmentStatus), default=AppointmentStatus.PENDING)
    expected_time = Column(DateTime, nullable=False)
    checkin_time = Column(DateTime)
    checkout_time = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    companion_num = Column(Integer, default=1)
    dingtalk_approval_id = Column(String(100))
    feedback = Column(String(500))
    batch_id = Column(Integer, ForeignKey("batch_appointments.id"))

    # Relationships
    host = relationship("User", back_populates="appointments_as_host")
    approval_logs = relationship("ApprovalLog", back_populates="appointment")
    check_in_logs = relationship("CheckInLog", back_populates="appointment")
    batch = relationship("BatchAppointment", back_populates="appointments")

    def to_dict(self):
        result = {
            'id': self.id,
            'visitor_name': self.visitor_name,
            'visitor_contact': self.visitor_contact,  # 注意：返回前需解密
            'vehicle_no': self.vehicle_no,  # 注意：返回前需解密
            'purpose': self.purpose,
            'host_id': self.host_id,
            'status': self.status.value,
            'expected_time': self.expected_time.isoformat(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'companion_num': self.companion_num,
            'feedback': self.feedback,
            'batch_id': self.batch_id
        }
        
        if self.checkin_time is not None:
            result['checkin_time'] = self.checkin_time.isoformat()
        if self.checkout_time is not None:
            result['checkout_time'] = self.checkout_time.isoformat()
            
        return result

    @classmethod
    def search_by_keyword(cls, keyword: str, date: Optional[datetime] = None):
        """
        搜索访客记录
        :param keyword: 搜索关键词（访客姓名或车牌号）
        :param date: 指定日期时间，默认为当天开始时间
        :return: 查询结果列表
        """
        if date is None:
            date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        return cls.query.join(User, User.id == cls.host_id)\
            .filter(
                cls.expected_time >= date,
                cls.expected_time < date + timedelta(days=1),
                or_(
                    cls.visitor_name.ilike(f'%{keyword}%'),
                    cls.vehicle_no.ilike(f'%{encryptor.encrypt(keyword)}%')
                )
            ).all()
