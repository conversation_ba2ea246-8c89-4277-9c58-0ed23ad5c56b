from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.appointment import Appointment, AppointmentStatus
from app.models.batch_appointment import BatchAppointment
from app.models.user import User
from app.database import db
from app.utils.encryption import encryptor
from app.utils.error_handlers import APIError
from app.middleware.auth import host_required
from datetime import datetime

bp = Blueprint('host', __name__, url_prefix='/host')

@bp.route('/pending', methods=['GET'])
@jwt_required()
@host_required
def get_pending_appointments():
    host_id = get_jwt_identity()
    appointments = Appointment.query.filter_by(
        host_id=host_id,
        status=AppointmentStatus.PENDING
    ).order_by(Appointment.expected_time.asc()).all()
    
    results = []
    for appointment in appointments:
        result = appointment.to_dict()
        result['visitor_contact'] = encryptor.decrypt(appointment.visitor_contact)
        result['vehicle_no'] = encryptor.decrypt(appointment.vehicle_no) if appointment.vehicle_no else None
        results.append(result)
    
    return jsonify(results)

@bp.route('/approve/<int:appointment_id>', methods=['PUT'])
@jwt_required()
@host_required
def approve_appointment(appointment_id):
    host_id = get_jwt_identity()
    appointment = Appointment.query.get_or_404(appointment_id)
    
    if appointment.host_id != host_id:
        raise APIError("Unauthorized", 403)
    
    data = request.get_json()
    new_status = data.get('status')
    if new_status not in ['approved', 'rejected']:
        raise APIError("Invalid status", 400)
    
    appointment.status = AppointmentStatus[new_status.upper()]
    db.session.commit()
    
    return jsonify(appointment.to_dict())

@bp.route('/batch', methods=['POST'])
@jwt_required()
@host_required
def create_batch_appointment():
    host_id = get_jwt_identity()
    data = request.get_json()
    
    # 创建批量预约记录
    batch = BatchAppointment(
        meeting_topic=data['meeting_topic'],
        purpose=data['purpose'],
        host_id=host_id,
        start_time=datetime.fromisoformat(data['start_time']),
        end_time=datetime.fromisoformat(data['end_time']),
        participants=data['participants']
    )
    db.session.add(batch)
    
    # 为每个参会者创建预约记录
    appointments = []
    for participant in data['participants']:
        appointment = Appointment(
            visitor_name=participant['name'],
            visitor_contact=encryptor.encrypt(participant['contact']),
            vehicle_no=encryptor.encrypt(participant.get('vehicle_no')) if participant.get('vehicle_no') else None,
            purpose=f"参加会议：{data['meeting_topic']}",
            host_id=host_id,
            expected_time=batch.start_time,
            status=AppointmentStatus.APPROVED,
            batch_id=batch.id
        )
        appointments.append(appointment)
    
    db.session.add_all(appointments)
    db.session.commit()
    
    return jsonify({
        "message": "Batch appointment created successfully",
        "batch_id": batch.id,
        "appointment_count": len(appointments)
    })

@bp.route('/profile', methods=['GET', 'PUT'])
@jwt_required()
@host_required
def manage_profile():
    host_id = get_jwt_identity()
    host = User.query.get_or_404(host_id)
    
    if request.method == 'GET':
        return jsonify(host.to_dict())
    
    data = request.get_json()
    if 'contact' in data:
        host.contact = data['contact']
    
    db.session.commit()
    return jsonify(host.to_dict()) 