from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Response, Form
from fastapi.responses import HTMLResponse
from models import <PERSON><PERSON>el<PERSON>
from typing import Optional
import secrets
from datetime import datetime, timedelta
from pydantic import BaseModel
from typing import List
import jwt
from jwt.exceptions import PyJWTError
from fastapi import Depends, HTTPException
from datetime import datetime, timedelta
from typing import Optional

SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 生成JWT令牌
async def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# 验证JWT令牌
async def verify_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

# 增强认证中间件
async def auth_verify(roles: Optional[list] = None, token: Optional[str] = None):
    if not token:
        raise HTTPException(status_code=401, detail="Missing token")
    payload = await verify_token(token)
    user_role = payload.get("role")
    if roles and user_role not in roles:
        raise HTTPException(status_code=403, detail="Forbidden")
    # 检查是否为管理员角色，如果是则允许访问管理员页面
    if user_role == 'admin':
        # 这里可以添加跳转到管理员页面的逻辑
        pass
    return payload

app = FastAPI()
db = DBHelper()

# 数据模型
class LoginForm(BaseModel):
    username: str
    password: str

class AppointmentForm(BaseModel):
    host_id: int
    visitor_name: str
    visitor_contact: str
    visit_reason: str
    vehicle_no: Optional[str] = None
    visit_time: datetime

class UserCreate(BaseModel):
    username: str
    password: str
    role: str
    department_id: Optional[int] = None

class BatchAppointmentForm(BaseModel):
    meeting_topic: str
    participants: List[dict]

class ApprovalForm(BaseModel):
    status: str

# 基础路由
@app.get("/", response_class=HTMLResponse)
async def read_root():
    with open("templates/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.get("/login", response_class=HTMLResponse)
async def login_page():
    try:
        with open("templates/login.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Login page not found")

# 解决重复定义问题，假设原来有重复定义的代码，这里简化处理
@app.post("/auth/login")
async def login(
    username: str = Form(...),
    password: str = Form(...)
):
    if db.verify_user(username, password):
        user = db.get_user(username)
        if not user:
            raise HTTPException(status_code=400, detail="用户名或密码错误")
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = await create_access_token(
            data={"sub": user['username'], "role": user['role']}, expires_delta=access_token_expires
        )
        return {"token": access_token}
    raise HTTPException(status_code=400, detail="用户名或密码错误")

# 实现 /admin/addUser 接口
@app.post("/admin/addUser")
async def add_user(user_data: UserCreate):
    try:
        # 这里添加用户到数据库的逻辑，假设 db 是数据库操作对象
        db.create_user(
            username=user_data.username,
            password=user_data.password,
            role=user_data.role,
            department_id=user_data.department_id
        )
        return {"message": "用户添加成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"用户添加失败: {str(e)}")

# 访客模块
@app.post("/visitor/submit")
async def submit_appointment(form: AppointmentForm, user: dict = Depends(auth_verify)):
    encrypted_contact = db._encrypt_data(form.visitor_contact)
    cursor = db.conn.cursor()
    cursor.execute('''INSERT INTO appointments 
        (visitor_name, visitor_contact, vehicle_no, host_id, status, checkin_time)
        VALUES (?,?,?,?,?,?)''',
        (form.visitor_name, encrypted_contact, form.vehicle_no, form.host_id, 'pending', None))
    db.conn.commit()
    return {"id": cursor.lastrowid}

@app.get("/visitor/history")
async def get_history(contact: str):
    encrypted_contact = db._encrypt_data(contact)
    cursor = db.conn.cursor()
    cursor.execute('''SELECT id,visitor_name,status,checkin_time 
        FROM appointments WHERE visitor_contact=?''', (encrypted_contact,))
    return {"data": cursor.fetchall()}

# 被访问人模块
@app.get("/host/pending")
async def get_pending(user: dict = Depends(auth_verify)):
    cursor = db.conn.cursor()
    cursor.execute('''SELECT id,visitor_name,visit_reason,visit_time 
        FROM appointments WHERE host_id=? AND status='pending' ''', (user.get('id'),))
    return {"data": cursor.fetchall()}

@app.put("/host/approve/{appointment_id}")
async def approve_appointment(appointment_id: int, form: ApprovalForm, user: dict = Depends(auth_verify)):
    cursor = db.conn.cursor()
    cursor.execute('''UPDATE appointments SET status=?
        WHERE id=? AND host_id=?''', (form.status, appointment_id, user["id"]))
    db.conn.commit()
    return {"updated": cursor.rowcount}

# 门卫模块
@app.get("/guard/search")
async def search_visitor(keyword: str, user: dict = Depends(auth_verify)):
    cursor = db.conn.cursor()
    cursor.execute('''SELECT id,visitor_name,vehicle_no,status 
        FROM appointments 
        WHERE visitor_name LIKE ? OR vehicle_no LIKE ?''',
        (f"%{keyword}%", f"%{keyword}%"))
    return {"data": cursor.fetchall()}

@app.post("/guard/checkin/{appointment_id}")
async def checkin_visitor(appointment_id: int, user: dict = Depends(auth_verify)):
    cursor = db.conn.cursor()
    cursor.execute('''UPDATE appointments SET checkin_time=datetime('now')
        WHERE id=? AND status='approved' ''', (appointment_id,))
    db.conn.commit()
    return {"updated": cursor.rowcount}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)