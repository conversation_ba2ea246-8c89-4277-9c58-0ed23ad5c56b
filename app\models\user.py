from datetime import datetime
from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>, Foreign<PERSON><PERSON>, DateTime, Boolean
from sqlalchemy.orm import relationship
from app.database import db
import bcrypt

class User(db.Model):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(20), nullable=False)
    department_id = Column(Integer, ForeignKey("departments.id"), nullable=True)
    contact = Column(String(255))  # Encrypted JSON string
    dingtalk_id = Column(String(100))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    department = relationship("Department", back_populates="users", foreign_keys=[department_id])
    appointments_as_host = relationship("Appointment", back_populates="host")
    logs = relationship("Log", back_populates="user")
    approval_logs = relationship("ApprovalLog", back_populates="approver", lazy='dynamic')
    check_in_logs = relationship("CheckInLog", back_populates="operator")
    blacklists = relationship("Blacklist", back_populates="creator")
    managed_departments = relationship("Department", back_populates="manager")
    batch_appointments = relationship("BatchAppointment", back_populates="host")

    def set_password(self, password: str):
        salt = bcrypt.gensalt()
        self.password_hash = bcrypt.hashpw(password.encode(), salt).decode()

    def check_password(self, password: str) -> bool:
        return bcrypt.checkpw(password.encode(), self.password_hash.encode())

    @property
    def is_admin(self):
        return self.role == 'admin'

    @property
    def is_host(self):
        return self.role == 'host'

    @property
    def is_guard(self):
        return self.role == 'guard'

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'role': self.role,
            'department_id': self.department_id,
            'contact': self.contact,
            'dingtalk_id': self.dingtalk_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
