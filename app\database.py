from typing import Any, Generator
from flask_sqlalchemy import SQLAlchemy
from flask_sqlalchemy.session import Session
from sqlalchemy.orm import DeclarativeBase
from contextlib import contextmanager

from sqlalchemy.orm.scoping import scoped_session

class Base(DeclarativeBase):
    pass

db = SQLAlchemy(model_class=Base)

@contextmanager
def db_session() -> Generator[scoped_session[Session], Any, None]:
    """提供安全的数据库会话上下文管理器"""
    try:
        yield db.session
        db.session.commit()
    except Exception:
        db.session.rollback()
        raise
    finally:
        db.session.close()

# Database initialization function
def init_db(app):
    db.init_app(app)
    
    with app.app_context():
        # Import all models here
        from app.models import user, appointment, log
        # Create all tables
        db.create_all() 