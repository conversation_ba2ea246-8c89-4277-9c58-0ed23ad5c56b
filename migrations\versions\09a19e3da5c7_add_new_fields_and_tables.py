"""add new fields and tables

Revision ID: 09a19e3da5c7
Revises: 40574ab87f81
Create Date: 2025-05-13 08:48:28.825076

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '09a19e3da5c7'
down_revision = '40574ab87f81'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('appointments', schema=None) as batch_op:
        batch_op.add_column(sa.Column('companion_num', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('dingtalk_approval_id', sa.String(length=100), nullable=True))
        batch_op.add_column(sa.Column('feedback', sa.String(length=500), nullable=True))
        batch_op.add_column(sa.Column('batch_id', sa.Integer(), nullable=True))
        batch_op.create_foreign_key('fk_appointments_batch_id', 'batch_appointments', ['batch_id'], ['id'])

    with op.batch_alter_table('departments', schema=None) as batch_op:
        batch_op.add_column(sa.Column('manager_id', sa.Integer(), nullable=True))
        batch_op.create_foreign_key('fk_departments_manager_id', 'users', ['manager_id'], ['id'])

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('dingtalk_id', sa.String(length=100), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_column('dingtalk_id')

    with op.batch_alter_table('departments', schema=None) as batch_op:
        batch_op.drop_constraint('fk_departments_manager_id', type_='foreignkey')
        batch_op.drop_column('manager_id')

    with op.batch_alter_table('appointments', schema=None) as batch_op:
        batch_op.drop_constraint('fk_appointments_batch_id', type_='foreignkey')
        batch_op.drop_column('batch_id')
        batch_op.drop_column('feedback')
        batch_op.drop_column('dingtalk_approval_id')
        batch_op.drop_column('companion_num')

    # ### end Alembic commands ###
