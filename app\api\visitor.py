from flask import Blueprint, request, jsonify
from app.models.appointment import Appointment, AppointmentStatus
from app.models.user import User
from app.database import db
from app.utils.encryption import encryptor
from app.utils.error_handlers import APIError
from datetime import datetime

bp = Blueprint('visitor', __name__, url_prefix='/visitor')

@bp.route('/register', methods=['POST'])
def register_visit():
    data = request.get_json()
    
    # 验证被访问人
    host = User.query.filter_by(id=data.get('host_id')).first()
    if not host or not host.is_host:
        raise APIError("Invalid host", 400)

    # 创建预约记录
    appointment = Appointment(
        visitor_name=data['visitor_name'],
        visitor_contact=encryptor.encrypt(data['visitor_contact']),
        vehicle_no=encryptor.encrypt(data.get('vehicle_no')) if data.get('vehicle_no') else None,
        purpose=data['purpose'],
        host_id=host.id,
        expected_time=datetime.fromisoformat(data['expected_time'])
    )
    
    db.session.add(appointment)
    db.session.commit()
    
    # 返回预约信息（解密敏感数据）
    result = appointment.to_dict()
    result['visitor_contact'] = data['visitor_contact']  # 返回原始未加密数据
    result['vehicle_no'] = data.get('vehicle_no')
    return jsonify(result)

@bp.route('/status/<int:appointment_id>', methods=['GET'])
def check_status(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    result = appointment.to_dict()
    # 解密敏感数据
    result['visitor_contact'] = encryptor.decrypt(appointment.visitor_contact)
    result['vehicle_no'] = encryptor.decrypt(appointment.vehicle_no) if appointment.vehicle_no else None
    return jsonify(result)

@bp.route('/history', methods=['GET'])
def get_history():
    contact = request.args.get('contact')
    if not contact:
        raise APIError("Contact information is required", 400)
    
    # 查找该联系方式的所有预约记录
    encrypted_contact = encryptor.encrypt(contact)
    appointments = Appointment.query.filter_by(visitor_contact=encrypted_contact)\
        .order_by(Appointment.created_at.desc()).all()
    
    results = []
    for appointment in appointments:
        result = appointment.to_dict()
        result['visitor_contact'] = contact  # 返回原始未加密数据
        result['vehicle_no'] = encryptor.decrypt(appointment.vehicle_no) if appointment.vehicle_no else None
        results.append(result)
    
    return jsonify(results) 