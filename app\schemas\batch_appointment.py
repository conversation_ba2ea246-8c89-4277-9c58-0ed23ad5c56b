from marshmallow import Schema, fields, validate
from datetime import datetime

class ParticipantSchema(Schema):
    name = fields.Str(required=True, validate=validate.Length(min=2, max=100))
    contact = fields.Str(required=True, validate=validate.Length(min=5, max=50))
    vehicle_no = fields.Str(validate=validate.Length(max=20))

class BatchAppointmentSchema(Schema):
    meeting_topic = fields.Str(required=True, validate=validate.Length(min=2, max=200))
    purpose = fields.Str(required=True, validate=validate.Length(min=5, max=500))
    start_time = fields.DateTime(required=True)
    end_time = fields.DateTime(required=True)
    participants = fields.List(fields.Nested(ParticipantSchema), required=True, validate=validate.Length(min=1))

    class Meta:
        unknown = True

batch_appointment_schema = BatchAppointmentSchema() 