from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app.database import db

class CheckInLog(db.Model):
    __tablename__ = "check_in_logs"
    
    id = Column(Integer, primary_key=True)
    appointment_id = Column(Integer, ForeignKey("appointments.id"), nullable=False)
    checkin_time = Column(DateTime, default=datetime.utcnow, nullable=False)
    gate = Column(String(50), nullable=False)  # 入口编号
    operator_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    appointment = relationship("Appointment", back_populates="check_in_logs")
    operator = relationship("User", back_populates="check_in_logs")
    
    def to_dict(self):
        return {
            'id': self.id,
            'appointment_id': self.appointment_id,
            'checkin_time': self.checkin_time.isoformat(),
            'gate': self.gate,
            'operator_id': self.operator_id
        }
