import click
from flask.cli import with_appcontext
from app.models.user import User
from app.database import db

@click.command('create-admin')
@click.option('--username', prompt=True)
@click.option('--password', prompt=True, hide_input=True)
@with_appcontext
def create_admin(username, password):
    """创建管理员用户"""
    user = User(
        username=username,
        role='admin'
    )
    user.set_password(password)
    db.session.add(user)
    db.session.commit()
    click.echo(f'Created admin user: {username}') 