from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app.database import db

class ApprovalLog(db.Model):
    __tablename__ = "approval_logs"
    
    id = Column(Integer, primary_key=True)
    appointment_id = Column(Integer, ForeignKey("appointments.id"), nullable=False)
    approver_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    action = Column(String(10), nullable=False)  # approve/reject
    feedback = Column(String(500))
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    appointment = relationship("Appointment", back_populates="approval_logs")
    approver = relationship("User", back_populates="approval_logs")
    
    def to_dict(self):
        return {
            'id': self.id,
            'appointment_id': self.appointment_id,
            'approver_id': self.approver_id,
            'action': self.action,
            'feedback': self.feedback,
            'timestamp': self.timestamp.isoformat()
        }
