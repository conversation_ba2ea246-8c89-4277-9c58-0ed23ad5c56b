from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from app.database import db

class BatchAppointment(db.Model):
    __tablename__ = "batch_appointments"
    
    id = Column(Integer, primary_key=True)
    meeting_topic = Column(String(200), nullable=False)
    purpose = Column(String(500), nullable=False)
    host_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=False)
    participants = Column(JSON, nullable=False)  # [{name, contact, vehicle_no}]
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    host = relationship("User", back_populates="batch_appointments")
    appointments = relationship("Appointment", back_populates="batch") 