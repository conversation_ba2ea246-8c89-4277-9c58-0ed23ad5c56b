from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, get_jwt_identity, jwt_required
from app.models.user import User
from app.database import db
from app.utils.error_handlers import APIError
from typing import Optional, Dict, Any

bp = Blueprint('auth', __name__, url_prefix='/auth')

@bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        raise APIError("Username and password are required")
    
    user: Optional[User] = User.query.filter_by(username=username).first()
    if not user or not user.check_password(password):
        raise APIError("Invalid username or password", 401)
    
    if not user.is_active:
        raise APIError("Account is disabled", 403)
    
    access_token = create_access_token(identity=user.id)
    response = jsonify({
        "access_token": access_token,
        "user": user.to_dict()
    })
    response.set_cookie(
        'access_token_cookie',
        value=access_token,
        httponly=True,
        secure=True,
        samesite='Lax'
    )
    return response

@bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    user_id = get_jwt_identity()
    user: Optional[User] = User.query.get(user_id)
    if not user:
        raise APIError("User not found", 404)
    return jsonify(user.to_dict())

@bp.route('/refresh', methods=['POST'])
@jwt_required()
def refresh_token():
    user_id = get_jwt_identity()
    new_token = create_access_token(identity=user_id)
    return jsonify({"access_token": new_token})

@bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    user_id = get_jwt_identity()
    user: Optional[User] = User.query.get(user_id)
    
    if not user:
        raise APIError("User not found", 404)
        
    data = request.get_json()
    old_password = data.get('old_password')
    new_password = data.get('new_password')
    
    if not old_password or not new_password:
        raise APIError("Old and new passwords are required")
    
    if not user.check_password(old_password):
        raise APIError("Invalid old password", 401)
    
    user.set_password(new_password)
    db.session.commit()
    
    return jsonify({"message": "Password updated successfully"})
