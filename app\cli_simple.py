import click
from flask.cli import with_appcontext
import bcrypt
from app.database import db
from sqlalchemy import text  

@click.command('create-admin-simple')
@click.option('--username', prompt=True)
@click.option('--password', prompt=True, hide_input=True)
@with_appcontext
def create_admin_simple(username, password):
    """创建管理员用户(简化版)"""
    # 直接使用 SQL 插入语句
    salt = bcrypt.gensalt()
    password_hash = bcrypt.hashpw(password.encode(), salt).decode()
    
    # 执行原始 SQL
    sql = text("""
    INSERT INTO users (username, password_hash, role, is_active, created_at, updated_at)
    VALUES (:username, :password_hash, 'admin', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    """)
    
    result = db.session.execute(sql, {
        'username': username,
        'password_hash': password_hash
    })
    db.session.commit()
    
    click.echo(f'Created admin user: {username}')