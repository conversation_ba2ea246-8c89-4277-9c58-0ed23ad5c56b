{"name": "birpc", "type": "module", "version": "0.2.19", "description": "Message based Two-way remote procedure call", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/birpc#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/birpc.git"}, "bugs": {"url": "https://github.com/antfu/birpc/issues"}, "keywords": ["rpc", "messages"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "devDependencies": {"@antfu/eslint-config": "^3.7.3", "@antfu/ni": "^0.23.0", "@types/node": "^22.7.5", "bumpp": "^9.6.1", "eslint": "^9.12.0", "esno": "^4.8.0", "typescript": "^5.6.2", "unbuild": "^2.0.0", "vite": "^5.4.8", "vitest": "^2.1.2"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "release": "bumpp && pnpm publish", "start": "esno src/index.ts", "typecheck": "tsc --noEmit", "test": "vitest"}}