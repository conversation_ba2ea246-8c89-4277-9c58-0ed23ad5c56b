<template>
  <div>
    <h1>管理员界面</h1>
    <!-- 用户管理页 -->
    <div>
      <h2>用户管理页</h2>
      <div>
        <label for="role-filter">角色筛选:</label>
        <select id="role-filter">
          <option value="host">被访问人</option>
          <option value="guard">门卫</option>
          <option value="admin">管理员</option>
        </select>
      </div>
      <table>
        <thead>
          <tr>
            <th>用户名</th>
            <th>角色</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <!-- 这里是用户列表 -->
        </tbody>
      </table>
      <button @click="addUser">新增用户</button>
      <button @click="editUser">编辑用户</button>
      <button @click="disableUser">禁用用户</button>
    </div>
    <!-- 访客管理页 -->
    <div>
      <h2>访客管理页</h2>
      <div>
        <label for="time-range">时间范围:</label>
        <input type="text" id="time-range">
        <label for="host-filter">被访问人:</label>
        <input type="text" id="host-filter">
        <label for="license-plate-filter">车牌号:</label>
        <input type="text" id="license-plate-filter">
      </div>
      <table>
        <thead>
          <tr>
            <th>访客姓名</th>
            <th>被访问人</th>
            <th>车牌号</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <!-- 这里是访客列表 -->
        </tbody>
      </table>
      <button @click="modifyVisitorInfo">修改访客信息</button>
      <button @click="viewOperationLog">查看操作日志</button>
    </div>
    <!-- 日志与统计页 -->
    <div>
      <h2>日志与统计页</h2>
      <table>
        <thead>
          <tr>
            <th>操作类型</th>
            <th>操作人</th>
            <th>时间</th>
          </tr>
        </thead>
        <tbody>
          <!-- 这里是日志列表 -->
        </tbody>
      </table>
      <div>
        <!-- 这里是统计图表 -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';

// 用户管理功能
const addUser = async () => {
  try {
    const response = await axios.post('/admin/addUser', {
      // 这里是新增用户的表单数据
    });
    console.log('新增用户成功');
  } catch (error) {
    console.error('新增用户失败', error);
  }
};

const editUser = async () => {
  try {
    const response = await axios.put('/admin/editUser', {
      // 这里是编辑用户的表单数据
    });
    console.log('编辑用户成功');
  } catch (error) {
    console.error('编辑用户失败', error);
  }
};

const disableUser = async () => {
  try {
    const response = await axios.put('/admin/disableUser', {
      // 这里是禁用用户的表单数据
    });
    console.log('禁用用户成功');
  } catch (error) {
    console.error('禁用用户失败', error);
  }
};

// 访客管理功能
const modifyVisitorInfo = async () => {
  try {
    const response = await axios.put('/admin/modifyVisitorInfo', {
      // 这里是修改访客信息的表单数据
    });
    console.log('修改访客信息成功');
  } catch (error) {
    console.error('修改访客信息失败', error);
  }
};

const viewOperationLog = async () => {
  try {
    const response = await axios.get('/admin/viewOperationLog');
    console.log('查看操作日志成功');
  } catch (error) {
    console.error('查看操作日志失败', error);
  }
};
</script>

<style scoped>
/* 样式 */
</style>