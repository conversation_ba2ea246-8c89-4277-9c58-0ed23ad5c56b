<template>
  <div>
    <h1>登录</h1>
    <form @submit.prevent="login">
      <label for="username">用户名:</label>
      <input type="text" id="username" v-model="username" required><br>
      <label for="password">密码:</label>
      <input type="password" id="password" v-model="password" required><br>
      <button type="submit">登录</button>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';

const username = ref('');
const password = ref('');

const login = async () => {
  try {
    const response = await axios.post('/auth/login', {
        username: username.value,
        password: password.value
    }, { headers: { 'Content-Type': 'application/json' } });
    const token = response.data.token;
    localStorage.setItem('token', token);
    console.log('登录成功');
    window.location.href = '/';
  } catch (error) {
    console.error('登录失败', error);
  }
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await axios.post('/auth/login', {
    username: username.value,
    password: password.value
}, { headers: { 'Content-Type': 'application/json' } });
const token = response.data.token;
localStorage.setItem('token', token);
console.log('登录成功');
window.location.href = '/';
} catch (error) {
console.error('登录失败', error);
}
};
</script>

<style scoped>
/* 样式 */
</style>
const response = await