<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访客登记系统</title>
    <style>
        .container { max-width: 600px; margin: 20px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h2>访客登记</h2>
        <form id="visitorForm" onsubmit="return submitForm(event)">
            <div class="form-group">
                <label>被访问人：</label>
                <select id="hostId" required>
                    <option value="1">张三（技术部）</option>
                    <option value="2">李四（行政部）</option>
                </select>
            </div>
            <div class="form-group">
                <label>访客姓名：</label>
                <input type="text" id="visitorName" required>
            </div>
            <div class="form-group">
                <label>联系方式：</label>
                <input type="tel" id="contact" pattern="[0-9]{11}" required>
            </div>
            <div class="form-group">
                <label>来访事由：</label>
                <textarea id="reason" rows="3" required></textarea>
            </div>
            <div class="form-group">
                <label>是否驾车：</label>
                <input type="checkbox" id="hasCar" onchange="toggleCarNo()">
            </div>
            <div class="form-group" id="carNoGroup" style="display:none;">
                <label>车牌号码：</label>
                <input type="text" id="carNo">
            </div>
            <button type="submit">提交登记</button>
        </form>
        <div id="result"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login';
            }
        });
        function toggleCarNo() {
            const group = document.getElementById('carNoGroup');
            group.style.display = document.getElementById('hasCar').checked ? 'block' : 'none';
        }

        async function submitForm(event) {
            event.preventDefault();
            
            const formData = {
                host_id: document.getElementById('hostId').value,
                visitor_name: document.getElementById('visitorName').value,
                visitor_contact: document.getElementById('contact').value,
                visit_reason: document.getElementById('reason').value,
                vehicle_no: document.getElementById('hasCar').checked 
                    ? document.getElementById('carNo').value 
                    : null
            };

            try {
                const response = await fetch('/visitor/submit', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + localStorage.getItem('token') },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                console.log('Received data:', data);
                document.getElementById('result').innerHTML = 
                    `登记成功！预约ID：${data.id}`;
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '提交失败，请稍后重试';
            }
        }
    </script>
</body>
</html>