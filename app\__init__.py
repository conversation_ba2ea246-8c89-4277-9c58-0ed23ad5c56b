from flask import Flask, render_template, send_from_directory, request
from flask_jwt_extended import J<PERSON><PERSON><PERSON><PERSON>, jwt_required
from flask_cors import CORS
from flask_caching import Cache
from app.config import config, settings

# Import models before db initialization
from app.models.user import User
from app.models.approval_log import ApprovalLog
from app.models.check_in_log import CheckInLog
from app.models.blacklist import Blacklist
from app.models.appointment import Appointment
from app.models.department import Department
from app.models.log import Log
from app.models.batch_appointment import BatchAppointment

from app.database import db

jwt = JWTManager()
cors = CORS()
cache = Cache()

def create_app(config_name='default'):
    app = Flask(__name__, template_folder='../templates', static_folder='../static')
    
    # 加载配置
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    cors.init_app(app)
    cache.init_app(app)
    
    @app.route('/')
    def index():
        # Check if user is authenticated via JWT in cookie
        jwt_token = request.cookies.get('access_token_cookie')
        if not jwt_token:
            return send_from_directory('../templates', 'login.html')
        try:
            jwt_required()(lambda: None)()  # Verify token
            return send_from_directory('../static', 'index.html')
        except:
            return send_from_directory('../templates', 'login.html')

    with app.app_context():
        # 注册错误处理器
        from app.utils.error_handlers import register_error_handlers
        register_error_handlers(app)
        
        # 注册蓝图 - 修改这里
        from app.api.auth import bp as auth_bp
        from app.api.visitor import bp as visitor_bp
        from app.api.host import bp as host_bp
        from app.api.guard import bp as guard_bp
        from app.api.admin import bp as admin_bp
        
        app.register_blueprint(auth_bp)
        app.register_blueprint(visitor_bp)
        app.register_blueprint(host_bp)
        app.register_blueprint(guard_bp)
        app.register_blueprint(admin_bp)
    
    return app
