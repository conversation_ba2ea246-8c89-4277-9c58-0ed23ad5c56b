from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from app.models.appointment import Appointment, AppointmentStatus
from app.models.user import User
from app.database import db
from app.utils.encryption import encryptor
from app.utils.error_handlers import APIError
from app.middleware.auth import guard_required
from datetime import datetime, timedelta
from sqlalchemy import or_

bp = Blueprint('guard', __name__, url_prefix='/guard')

@bp.route('/search', methods=['GET'])
@jwt_required()
@guard_required
def search_visitor():
    keyword = request.args.get('keyword', '')
    if not keyword:
        raise APIError("Search keyword is required", 400)
    
    # 搜索当天的预约记录
    today = datetime.now().date()
    appointments = Appointment.query.join(User, User.id == Appointment.host_id)\
        .filter(
            Appointment.expected_time >= today,
            Appointment.expected_time < today + timedelta(days=1),
            or_(
                Appointment.visitor_name.ilike(f'%{keyword}%'),
                Appointment.vehicle_no.ilike(f'%{encryptor.encrypt(keyword)}%')
            )
        ).all()
    
    results = []
    for appointment in appointments:
        result = appointment.to_dict()
        result['visitor_contact'] = encryptor.decrypt(appointment.visitor_contact)
        result['vehicle_no'] = encryptor.decrypt(appointment.vehicle_no) if appointment.vehicle_no else None
        result['host_name'] = appointment.host.username
        result['host_department'] = appointment.host.department.name if appointment.host.department else None
        results.append(result)
    
    return jsonify(results)

@bp.route('/checkin/<int:appointment_id>', methods=['POST'])
@jwt_required()
@guard_required
def checkin_visitor(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    
    if appointment.status != AppointmentStatus.APPROVED:
        raise APIError("Appointment is not approved", 400)
    
    if appointment.checkin_time:
        raise APIError("Visitor has already checked in", 400)
    
    appointment.checkin_time = datetime.utcnow()
    appointment.status = AppointmentStatus.COMPLETED
    db.session.commit()
    
    return jsonify(appointment.to_dict())

@bp.route('/checkout/<int:appointment_id>', methods=['POST'])
@jwt_required()
@guard_required
def checkout_visitor(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    
    if not appointment.checkin_time:
        raise APIError("Visitor has not checked in", 400)
    
    if appointment.checkout_time:
        raise APIError("Visitor has already checked out", 400)
    
    appointment.checkout_time = datetime.utcnow()
    db.session.commit()
    
    return jsonify(appointment.to_dict())

@bp.route('/dashboard', methods=['GET'])
@jwt_required()
@guard_required
def get_dashboard():
    today = datetime.now().date()
    
    # 获取今日预约统计
    appointments = Appointment.query.filter(
        Appointment.expected_time >= today,
        Appointment.expected_time < today + timedelta(days=1)
    ).all()
    
    stats = {
        'total': len(appointments),
        'checked_in': len([a for a in appointments if a.checkin_time]),
        'pending': len([a for a in appointments if a.status == AppointmentStatus.PENDING]),
        'approved': len([a for a in appointments if a.status == AppointmentStatus.APPROVED]),
        'completed': len([a for a in appointments if a.status == AppointmentStatus.COMPLETED])
    }
    
    # 获取最近的访客记录
    recent_appointments = []
    for appointment in appointments[:10]:  # 最近10条记录
        result = appointment.to_dict()
        result['visitor_contact'] = encryptor.decrypt(appointment.visitor_contact)
        result['vehicle_no'] = encryptor.decrypt(appointment.vehicle_no) if appointment.vehicle_no else None
        result['host_name'] = appointment.host.username
        recent_appointments.append(result)
    
    return jsonify({
        'stats': stats,
        'recent_appointments': recent_appointments
    }) 