from flask import jsonify
from werkzeug.exceptions import HTTPException
from sqlalchemy.exc import SQLAlchemyError
import logging

logger = logging.getLogger(__name__)

class APIError(Exception):
    def __init__(self, message, status_code=400, error_code=None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.error_code = error_code or status_code

    def to_dict(self):
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code
        }

def register_error_handlers(app):
    @app.errorhandler(APIError)
    def handle_api_error(error):
        logger.error(f"API Error: {error.message}")
        response = jsonify(error.to_dict())
        response.status_code = error.status_code
        return response

    @app.errorhandler(SQLAlchemyError)
    def handle_db_error(error):
        logger.error(f"Database Error: {str(error)}")
        return jsonify({
            "error": "DatabaseError",
            "message": "An internal database error occurred"
        }), 500

    @app.errorhandler(Exception)
    def handle_generic_error(error):
        response = {
            "error": "Internal Server Error",
            "message": str(error),
            "status_code": 500
        }
        return jsonify(response), 500 