import os
from datetime import timedelta
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

class Settings:
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-key')
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt-key')
    AES_KEY = os.getenv('AES_KEY', 'default-aes-key-must-be-32-bytes-long')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=30)
    SQLALCHEMY_TRACK_MODIFICATIONS = False

class BaseConfig:
    SECRET_KEY = Settings.SECRET_KEY
    JWT_SECRET_KEY = Settings.JWT_SECRET_KEY
    JWT_ACCESS_TOKEN_EXPIRES = Settings.JWT_ACCESS_TOKEN_EXPIRES
    SQLALCHEMY_TRACK_MODIFICATIONS = Settings.SQLALCHEMY_TRACK_MODIFICATIONS
    
    # 缓存配置
    CACHE_TYPE = os.getenv('CACHE_TYPE', 'simple')
    CACHE_DEFAULT_TIMEOUT = int(os.getenv('CACHE_DEFAULT_TIMEOUT', 300))

class DevelopmentConfig(BaseConfig):
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', 'sqlite:///dev.db')

class TestingConfig(BaseConfig):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///test.db'

class ProductionConfig(BaseConfig):
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL')

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

settings = Settings() 