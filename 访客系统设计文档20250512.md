准备开发一个访客管理系统，解决单位来访人员的审核和记录。主要流程是1、访客通过微信、钉钉扫描二维码或者直接网址进入申请界面，填写访客姓名、微信或者钉钉标识、联系电话、车辆牌号、接访人姓名、联系电话、来访原因、访问时间等信息后，通过钉钉发送给被访问人审核，审核不通过直接反馈访客微信或者钉钉。通过的话进入接访信息库。2、访客到达后通过扫码或者门卫输入来访者电话查找访客本次来访信息，如果没有问题在开门放行，系统通过钉钉提示被访问人访客到达信息。3、对于开会或者举行活动邀请则由被访问人或者组织者将相应信息通过批量申请界面导入直接同意申请进入接访信息库。
1、请完善以上流程，撰写一份规范的需求说明和功能设计。
2、要细分需要生成的界面及其数据，规划UI和api接口定义。
3、访客管理系统交互细节详细设计。



# **访客管理系统需求说明与功能设计**

## **1. 项目背景**
为提升单位来访人员管理效率，实现访客申请、审核、登记、放行全流程数字化管理，减少人工操作，提高安全性和便捷性，特开发本访客管理系统。系统支持微信、钉钉扫码或网页申请，并与钉钉集成，实现自动审批、访客到达提醒等功能。

---

## **2. 需求说明**
### **2.1 核心功能**
#### **(1) 访客申请**
- **申请方式**：
  - 访客通过 **微信/钉钉扫码** 进入申请页面。
  - 访客通过 **网页链接** 直接填写申请表单。
- **申请信息**：
  - 必填项：访客姓名、联系方式（手机号）、接访人姓名、接访人联系方式、来访原因、访问时间。
  - 选填项：车辆牌号（如有）、同行人数、单位名称。
- **提交方式**：
  - 提交后，系统自动通过 **钉钉消息** 发送审批请求给接访人。

#### **(2) 审批流程**
- **审批方式**：
  - 接访人通过 **钉钉审批** 进行审核（同意/拒绝）。
  - 审批不通过时，系统自动通过 **微信/钉钉** 通知访客，并说明原因。
  - 审批通过后，访客信息存入 **接访信息库**，并生成 **访客通行码**（可关联二维码）。
- **批量审批**（适用于会议或活动）：
  - 接访人或组织者可通过 **Excel 批量导入** 访客名单，系统自动审批通过并入库。

#### **(3) 访客到达登记**
- **登记方式**：
  - **扫码登记**：访客到达后，扫描 **门禁二维码**，系统自动匹配访客信息。
  - **手动查询**：门卫可通过 **访客手机号** 查询访客信息。
- **放行逻辑**：
  - 系统检查访客是否 **已审批** 且 **在预约时间内**：
    - 符合条件：自动放行，并通过 **钉钉** 通知接访人“访客已到达”。
    - 不符合条件：提示 **“访客未预约”** 或 **“访问时间未到”**，需人工处理。

#### **(4) 数据管理**
- **访客记录存储**：
  - 存储访客申请、审批、到达记录，支持按 **时间、接访人、访客姓名** 查询。
- **黑名单管理**：
  - 可标记异常访客（如多次未预约强行进入），限制其申请。

---

### **2.2 非功能性需求**
1. **安全性**：
   - 访客手机号、车牌号等敏感信息加密存储。
   - 仅接访人、门卫、管理员有权限查看访客数据。
2. **高可用性**：
   - 支持 **1000+ 并发访客申请**，响应时间 < 2s。
3. **多平台兼容**：
   - 适配微信、钉钉、PC 浏览器访问。
4. **日志审计**：
   - 记录所有访客操作（申请、审批、放行）供追溯。

---

## **3. 功能设计**
### **3.1 系统架构**
```
访客终端（微信/钉钉/Web）  
    │  
    ▼  
API 服务层（访客申请、审批、查询）  
    │  
    ▼  
数据库（访客信息、审批记录）  
    │  
    ▼  
钉钉/微信消息通知  
    │  
    ▼  
门禁系统对接（扫码放行）
```

### **3.2 核心模块**
| **模块**          | **功能**                                                                 |
|-------------------|-------------------------------------------------------------------------|
| **访客申请模块**  | 提供申请表单、提交至审批、生成访客通行码                                |
| **审批管理模块**  | 对接钉钉审批，支持单条/批量审批                                        |
| **访客登记模块**  | 扫码/手动查询访客信息，控制门禁放行                                    |
| **数据统计模块**  | 按日/周/月统计访客量，支持导出 Excel                                   |
| **系统管理模块**  | 黑名单管理、权限配置、日志审计                                         |

### **3.3 数据库设计（关键表）**
#### **(1) `visitor_info`（访客信息表）**
| 字段            | 类型       | 说明                     |
|----------------|-----------|-------------------------|
| visitor_id     | BIGINT    | 主键，唯一标识           |
| name           | VARCHAR   | 访客姓名                 |
| phone          | VARCHAR   | 访客手机号（加密存储）    |
| car_plate      | VARCHAR   | 车牌号（可选）           |
| interviewee    | VARCHAR   | 接访人姓名               |
| reason         | TEXT      | 来访原因                 |
| visit_time     | DATETIME  | 预约访问时间             |
| status         | ENUM      | 状态（待审核/通过/拒绝） |

#### **(2) `approval_log`（审批记录表）**
| 字段            | 类型       | 说明                     |
|----------------|-----------|-------------------------|
| log_id         | BIGINT    | 审批记录 ID              |
| visitor_id     | BIGINT    | 关联访客 ID              |
| approver       | VARCHAR   | 审批人（钉钉 userid）    |
| result         | ENUM      | 审批结果（通过/拒绝）    |
| feedback       | TEXT      | 审批意见（可选）         |

#### **(3) `check_in_log`（签到记录表）**
| 字段            | 类型       | 说明                     |
|----------------|-----------|-------------------------|
| check_id       | BIGINT    | 签到记录 ID              |
| visitor_id     | BIGINT    | 关联访客 ID              |
| check_time     | DATETIME  | 实际到达时间             |
| gate           | VARCHAR   | 入口编号（如南门/北门）  |

---

## **4. 扩展性考虑**
1. **对接人脸识别**：未来可扩展 **刷脸通行**，提升无接触体验。
2. **访客评价**：接访人可对访客行为评分，用于黑名单管理。
3. **访客预约限制**：可设置 **每日最大访客数**，避免过多人员进入。

---

## **5. 交付物**
1. **需求文档**（本文档）。
2. **原型设计**（Axure/墨刀交互稿）。
3. **API 接口文档**（供钉钉对接使用）。
4. **数据库 ER 图**（PowerDesigner 或 MySQL Workbench）。

---

### **6. 后续优化方向**
- 增加 **访客自助签离** 功能（扫码离开，记录停留时间）。
- 对接 **单位内部通讯录**，自动匹配接访人信息。
- 支持 **访客预约时间段选择**（如 9:00-10:00）。

---

此方案涵盖了核心业务流程，如需进一步细化（如 UI 设计、API 接口定义），可继续补充。




# **访客管理系统UI界面与API接口详细设计**

## **1. UI界面规划**
系统主要包含 **访客端（微信/钉钉H5）、审批端（钉钉）、门卫管理端（Web）、后台管理端（Web）**，各端界面及数据流如下：

---

### **1.1 访客申请端（H5）**
#### **(1) 访客申请表单页**
**UI元素**：
- 标题："访客预约申请"
- 表单字段：
  - 访客姓名（`visitor_name`，必填，文本输入）
  - 联系方式（`phone`，必填，手机号校验）
  - 车牌号（`car_plate`，选填，带"无车辆"选项）
  - 接访人（`interviewee`，必填，支持搜索单位通讯录）
  - 来访原因（`reason`，必填，下拉选择：公务/会议/私人拜访/其他）
  - 访问时间（`visit_time`，必填，日期时间选择器）
  - 同行人数（`companion_num`，数字输入，默认1）
- 提交按钮（触发API：`/api/visitor/apply`）

**数据示例（JSON）**：
```json
{
  "visitor_name": "张三",
  "phone": "13800138000",
  "car_plate": "京A12345",
  "interviewee": "李四",
  "reason": "会议",
  "visit_time": "2024-05-20 14:00:00",
  "companion_num": 2
}
```

#### **(2) 申请结果页**
- 审批中：显示"您的申请已提交，等待接访人审核" + 倒计时自动刷新。
- 审批通过：显示"审批通过" + 访客通行二维码（含`visitor_id`）。
- 审批拒绝：显示拒绝原因（`feedback`字段） + 重新申请按钮。

---

### **1.2 审批端（钉钉工作台）**
#### **(1) 审批消息卡片**
**UI元素**：
- 标题："新的访客申请：张三（138****8000）"
- 关键信息：
  - 访客姓名、电话、来访时间、原因
  - 接访人姓名（当前用户）
- 操作按钮：
  - "同意"（调用API：`/api/approval/accept`）
  - "拒绝"（弹出理由输入框，调用API：`/api/approval/reject`）

**数据交互**：
```json
// 同意请求
{
  "approval_id": "123",
  "action": "accept"
}

// 拒绝请求
{
  "approval_id": "123",
  "action": "reject",
  "feedback": "预约时间冲突"
}
```

#### **(2) 批量导入页面（Web）**
**UI元素**：
- Excel模板下载按钮
- 文件上传区域（支持.xlsx）
- 提交按钮（调用API：`/api/visitor/batch-import`）

---

### **1.3 门卫管理端（Web）**
#### **(1) 访客查询页**
**UI元素**：
- 搜索框（支持手机号、车牌号、访客姓名）
- 列表展示匹配的访客信息：
  - 访客姓名、电话、预约时间、接访人
  - "放行"按钮（调用API：`/api/check-in`）

**数据示例**：
```json
{
  "visitor_id": "1001",
  "visitor_name": "张三",
  "phone": "138****8000",
  "interviewee": "李四",
  "status": "approved" // 仅显示已审批的访客
}
```

#### **(2) 扫码结果页**
- 显示访客信息 + 放行按钮（同查询页）。

---

### **1.4 后台管理端（Web）**
#### **(1) 访客记录管理**
- 表格展示所有访客记录，支持按时间、状态筛选。
- 导出Excel按钮（调用API：`/api/visitor/export`）。

#### **(2) 黑名单管理**
- 添加黑名单（输入手机号/车牌号 + 原因）。

---

## **2. API接口定义**
### **2.1 访客申请模块**
| **端点**                  | **方法** | **请求参数**                                                                 | **响应**                                                                 |
|--------------------------|---------|-----------------------------------------------------------------------------|-------------------------------------------------------------------------|
| `/api/visitor/apply`     | POST    | `visitor_name`, `phone`, `interviewee`, `reason`, `visit_time`（见1.1）     | `{ code: 200, data: { visitor_id: "1001" } }`                           |
| `/api/visitor/status`    | GET     | `visitor_id`（或`phone`）                                                   | `{ status: "approved", qrcode_url: "..." }`                             |
| `/api/visitor/batch-import` | POST  | `file`（Excel文件）                                                         | `{ success_count: 10, fail_list: [...] }`                               |

### **2.2 审批模块**
| **端点**                  | **方法** | **请求参数**                              | **响应**                                              |
|--------------------------|---------|------------------------------------------|------------------------------------------------------|
| `/api/approval/accept`   | POST    | `approval_id`                            | `{ code: 200 }`                                      |
| `/api/approval/reject`   | POST    | `approval_id`, `feedback`                | `{ code: 200 }`                                      |
| `/api/approval/pending`  | GET     | `user_id`（钉钉ID）                      | `[ { approval_id: "123", visitor_name: "张三", ... } ]` |

### **2.3 签到放行模块**
| **端点**                | **方法** | **请求参数**                | **响应**                                                                 |
|------------------------|---------|----------------------------|-------------------------------------------------------------------------|
| `/api/check-in`        | POST    | `visitor_id`, `gate`（门编号） | `{ code: 200, notify: true }`（`notify`表示是否已通知接访人）           |
| `/api/check-in/search` | GET     | `phone`（或`visitor_id`）    | `{ visitor_id: "1001", name: "张三", status: "approved", ... }`         |

### **2.4 数据管理模块**
| **端点**                | **方法** | **参数**                   | **响应**                                |
|------------------------|---------|----------------------------|----------------------------------------|
| `/api/visitor/export`  | GET     | `start_date`, `end_date`   | Excel文件下载                          |
| `/api/blacklist/add`   | POST    | `phone`, `reason`          | `{ code: 200 }`                        |

---

## **3. 安全与权限设计**
- **接口鉴权**：
  - 访客端：JWT Token（微信/钉钉登录后获取）。
  - 管理端：RBAC（角色：门卫、管理员、超级管理员）。
- **数据过滤**：
  - 门卫只能查询当天访客记录。
  - 审批人只能操作自己的待审批项。

---

## **4. 原型示意图（关键界面）**
1. **访客申请页**  
   ![申请页](https://example.com/visitor-apply.png)  
   *说明：表单布局需适配移动端，字段带校验提示。*

2. **审批消息卡片（钉钉）**  
   ![审批卡片](https://example.com/approval-card.png)  
   *说明：集成钉钉工作通知，一键操作。*

3. **门卫查询页**  
   ![门卫查询](https://example.com/guard-search.png)  
   *说明：支持快速搜索和批量放行。*

---

此设计可直接交付前端与后端开发，如需交互细节或接口字段补充，可进一步细化。


# **访客管理系统交互细节详细设计**

## **1. 访客申请端交互流程**
### **1.1 访客申请表单页**
#### **字段级交互**
| **字段**         | **交互规则**                                                                 | **校验逻辑**                                                                 |
|------------------|-----------------------------------------------------------------------------|-----------------------------------------------------------------------------|
| **访客姓名**     | - 输入框获得焦点时，底部显示灰色提示文字"请输入真实姓名"<br>- 输入非空后提示消失          | - 必填<br>- 长度限制：2-20个字符<br>- 仅允许中文、英文、空格                                |
| **联系方式**     | - 输入时自动格式化（如`138 0013 8000`）<br>- 输入完成后显示"发送验证码"按钮（可选）       | - 必填<br>- 正则校验：`/^1[3-9]\d{9}$/`<br>- 禁止重复预约（同一手机号当天只能申请3次）         |
| **车牌号**       | - 选择"有车辆"时显示输入框<br>- 输入时自动大写字母（如"京A"→"京A"）                     | - 选填<br>- 正则校验：`/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}[A-Z]{1}[A-Z0-9]{5}$/` |
| **接访人**       | - 输入时调用API `/api/user/search?keyword=李` 实时搜索<br>- 点击结果项自动填充姓名和部门   | - 必填<br>- 接访人必须在单位通讯录中存在                                                  |
| **来访原因**     | - 下拉选项：公务/会议/私人拜访/其他<br>- 选择"其他"时显示备注输入框                     | - 必填                                                                      |
| **访问时间**     | - 点击弹出日期时间选择器<br>- 默认当前时间+30分钟<br>- 限制选择范围：当天8:00-18:00       | - 必填<br>- 不能选择过去时间<br>- 同一接访人30分钟内最多3个访客                            |

#### **提交交互**
1. 点击"提交"按钮时：
   - 触发所有字段的即时校验
   - 如有错误，在错误字段下方显示红色提示（如"请输入有效的手机号"）
2. 校验通过后：
   - 按钮变为加载状态（旋转图标+禁用）
   - 调用`/api/visitor/apply`提交数据
   - 成功响应后跳转到**申请结果页**

#### **异常处理**
| **场景**                 | **用户反馈**                                                                 |
|--------------------------|-----------------------------------------------------------------------------|
| 网络中断                 | 显示Toast提示"网络连接失败，请重试"，按钮恢复可点击状态                              |
| 接访人不存在             | 在接访人输入框下方显示红色提示"该员工不存在，请重新选择"                               |
| 时间段冲突               | 弹出对话框："您选择的时段已有其他访客预约，建议修改为14:30或其他时间" + 重新选择时间控件 |

---

## **2. 审批端交互细节（钉钉）**
### **2.1 审批消息卡片**
#### **卡片操作流**
```mermaid
sequenceDiagram
  访客->>系统: 提交申请
  系统->>钉钉: 发送审批卡片（含"同意"/"拒绝"按钮）
  alt 点击"同意"
    钉钉->>系统: POST /api/approval/accept
    系统->>钉钉: 返回成功
    钉钉UI: 卡片更新为"已同意"状态（绿色标签）
  else 点击"拒绝"
    钉钉UI: 弹出理由输入框（最多100字）
    用户输入后->>系统: POST /api/approval/reject
    系统->>访客: 推送拒绝通知（含理由）
  end
```

#### **多级审批（可选）**
- 如果开启二级审批（如部门主管复核）：
  - 第一级接访人同意后，卡片变为"待部门审批"状态
  - 新增操作按钮："加急处理"（触发钉钉电话提醒）

---

## **3. 门卫管理端交互**
### **3.1 扫码/搜索流程**
```mermaid
flowchart TD
  A[访客到达] --> B{识别方式}
  B -->|扫码| C[解析二维码中的visitor_id]
  B -->|手动输入| D[输入手机号后4位]
  C & D --> E[调用/api/check-in/search]
  E --> F{验证结果}
  F -->|成功| G[显示访客信息+绿色"放行"按钮]
  F -->|失败| H[红色提示"未找到预约记录"]
  G --> I[点击放行]
  I --> J[调用/api/check-in]
  J --> K[闸机开门+播放"请通行"语音]
```

### **3.2 异常处理**
| **场景**                 | **交互响应**                                                                 |
|--------------------------|-----------------------------------------------------------------------------|
| 访客提前到达             | 显示黄色提示："访客预约时间为14:00，当前13:30" + 禁用放行按钮（可手动覆盖）           |
| 黑名单访客               | 弹出警示框："该访客在黑名单中，请联系安保主管" + 记录异常事件                         |
| 重复放行                 | Toast提示："该访客已于13:45放行" + 显示历史记录                                 |

---

## **4. 后台管理端交互**
### **4.1 数据表格操作**
#### **批量操作交互**
1. 勾选多条记录 → 显示悬浮工具栏（含"导出"、"批量删除"）
2. 点击"导出"：
   - 弹出对话框选择日期范围
   - 导出后显示"已生成10条记录.csv"下载链接

#### **右键上下文菜单**
| **操作项**       | **行为**                                                                 |
|------------------|-------------------------------------------------------------------------|
| 查看详情         | 侧滑面板展示完整访客轨迹（申请/审批/到达时间）                                  |
| 加入黑名单       | 需填写原因，并同步通知门卫端                                                |
| 手动补登记       | 用于处理未预约但已放行的情况，需录入实际到达时间                                |

---

## **5. 关键动画与微交互**
1. **提交按钮**：
   - 点击时产生波纹效果（Ripple Animation）
   - 成功提交后显示✅图标+渐隐动画
2. **审批状态更新**：
   - 卡片状态变更时使用Flip动画（正面→背面旋转）
3. **门卫放行**：
   - 点击"放行"按钮时，按钮变为绿色进度条（2秒倒计时后触发开门）

---

## **6. 埋点设计（用户行为追踪）**
| **事件**              | **触发时机**                     | **上报数据**                                |
|-----------------------|---------------------------------|-------------------------------------------|
| visitor_apply_start   | 进入申请页时                    | `{ source: 'wechat' }`                   |
| visitor_apply_submit  | 点击提交按钮                    | `{ field_errors: [] }`                   |
| approval_action       | 审批操作时                      | `{ action: 'reject', delay_seconds: 30 }`|
| gate_check_in         | 门卫放行时                      | `{ method: 'scan', gate_id: 'G1' }`      |

---

## **7. 边缘场景处理**
### **7.1 审批超时**
- 规则：超过4小时未审批自动拒绝
- 交互：
  1. 访客端显示："接访人未及时处理，请电话联系"
  2. 审批端卡片变为灰色（"已超时"状态）

### **7.2 访客迟到**
- 规则：超过预约时间30分钟未到达自动关闭预约
- 门卫端显示："该预约已失效，需重新申请"

---

此设计覆盖核心交互场景，可与UI设计稿和API文档直接对接。需要特别关注：
1. **移动端表单的输入体验**（如车牌号键盘自动切换英文）
2. **审批状态的实时同步**（钉钉卡片与系统数据一致性）
3. **离线模式处理**（门卫端断网时支持本地缓存最新100条记录）