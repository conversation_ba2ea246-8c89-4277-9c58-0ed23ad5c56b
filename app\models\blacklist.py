from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app.database import db

class Blacklist(db.Model):
    __tablename__ = "blacklists"
    
    id = Column(Integer, primary_key=True)
    phone = Column(String(20), nullable=False)  # 加密存储
    reason = Column(String(500), nullable=False)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    creator = relationship("User", back_populates="blacklists")
    
    def to_dict(self):
        return {
            'id': self.id,
            'phone': self.phone,
            'reason': self.reason,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat()
        }
