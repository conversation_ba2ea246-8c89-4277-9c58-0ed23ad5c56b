from marshmallow import Schema, fields, validate
from datetime import datetime

class AppointmentSchema(Schema):
    visitor_name = fields.Str(required=True, validate=validate.Length(min=2, max=100))
    visitor_contact = fields.Str(required=True, validate=validate.Length(min=5, max=50))
    vehicle_no = fields.Str(validate=validate.Length(max=20))
    purpose = fields.Str(required=True, validate=validate.Length(min=5, max=500))
    host_id = fields.Int(required=True)
    expected_time = fields.DateTime(required=True)

    class Meta:
        unknown = True

appointment_schema = AppointmentSchema() 