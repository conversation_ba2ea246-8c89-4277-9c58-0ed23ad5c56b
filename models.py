import sqlite3
from hashlib import sha256
import os
import json
from typing import Optional
from enum import Enum

class DBHelper:
    def __init__(self, db_path='visitor.db'):
        self.conn = sqlite3.connect(db_path)
        self._create_tables()
        self.encryption_key = os.environ.get('ENCRYPTION_KEY', 'default_secret').encode()

    def _create_tables(self):
        cursor = self.conn.cursor()
        # 用户表
        cursor.execute('''CREATE TABLE IF NOT EXISTS users
                        (id INTEGER PRIMARY KEY AUTOINCREMENT,
                         username TEXT UNIQUE,
                         password_hash TEXT,
                         role TEXT CHECK(role IN ('visitor','host','guard','admin')),
                         department_id INTEGER,
                         contact TEXT,
                         dingtalk_id TEXT)''')
        
        # 访客预约表
        cursor.execute('''CREATE TABLE IF NOT EXISTS appointments
                        (id INTEGER PRIMARY KEY AUTOINCREMENT,
                         visitor_name TEXT NOT NULL,
                         visitor_contact TEXT NOT NULL,
                         vehicle_no TEXT,
                         host_id INTEGER NOT NULL,
                         status TEXT CHECK(status IN ('pending','approved','rejected','completed')) DEFAULT 'pending',
                         purpose TEXT NOT NULL,
                         expected_time DATETIME NOT NULL,
                         companion_num INTEGER DEFAULT 1,
                         checkin_time DATETIME,
                         checkout_time DATETIME,
                         feedback TEXT,
                         dingtalk_approval_id TEXT,
                         batch_id INTEGER,
                         FOREIGN KEY(host_id) REFERENCES users(id))''')
        
        # 批量预约表
        cursor.execute('''CREATE TABLE IF NOT EXISTS batch_appointments
                        (id INTEGER PRIMARY KEY AUTOINCREMENT,
                         meeting_topic TEXT NOT NULL,
                         purpose TEXT NOT NULL,
                         host_id INTEGER NOT NULL,
                         start_time DATETIME NOT NULL,
                         end_time DATETIME NOT NULL,
                         participants TEXT NOT NULL,
                         FOREIGN KEY(host_id) REFERENCES users(id))''')
        
        # 审批记录表
        cursor.execute('''CREATE TABLE IF NOT EXISTS approval_logs
                        (id INTEGER PRIMARY KEY AUTOINCREMENT,
                         appointment_id INTEGER NOT NULL,
                         approver_id INTEGER NOT NULL,
                         action TEXT CHECK(action IN ('approve','reject')) NOT NULL,
                         feedback TEXT,
                         timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                         FOREIGN KEY(appointment_id) REFERENCES appointments(id),
                         FOREIGN KEY(approver_id) REFERENCES users(id))''')
        
        # 签到记录表
        cursor.execute('''CREATE TABLE IF NOT EXISTS check_in_logs
                        (id INTEGER PRIMARY KEY AUTOINCREMENT,
                         appointment_id INTEGER NOT NULL,
                         checkin_time DATETIME NOT NULL,
                         gate TEXT NOT NULL,
                         operator_id INTEGER NOT NULL,
                         FOREIGN KEY(appointment_id) REFERENCES appointments(id),
                         FOREIGN KEY(operator_id) REFERENCES users(id))''')
        
        # 黑名单表
        cursor.execute('''CREATE TABLE IF NOT EXISTS blacklists
                        (id INTEGER PRIMARY KEY AUTOINCREMENT,
                         phone TEXT NOT NULL,
                         reason TEXT NOT NULL,
                         created_by INTEGER NOT NULL,
                         created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                         FOREIGN KEY(created_by) REFERENCES users(id))''')
        
        # 操作日志表
        cursor.execute('''CREATE TABLE IF NOT EXISTS logs
                        (id INTEGER PRIMARY KEY AUTOINCREMENT,
                         user_id INTEGER NOT NULL,
                         action TEXT NOT NULL,
                         target_type TEXT NOT NULL,
                         target_id INTEGER NOT NULL,
                         details TEXT,
                         timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                         FOREIGN KEY(user_id) REFERENCES users(id))''')
        
        self.conn.commit()

    def _encrypt_data(self, data: str) -> str:
        return sha256(data.encode() + self.encryption_key).hexdigest()

    def create_user(self, username: str, password: str, role: str, department_id: Optional[int] = None):
        password_hash = sha256(password.encode()).hexdigest()
        cursor = self.conn.cursor()
        cursor.execute('INSERT INTO users (username, password_hash, role, department_id) VALUES (?,?,?,?)',
                      (username, password_hash, role, department_id))
        self.conn.commit()

    def verify_user(self, username: str, password: str) -> bool:
        cursor = self.conn.cursor()
        cursor.execute('SELECT password_hash FROM users WHERE username=?', (username,))
        result = cursor.fetchone()
        return sha256(password.encode()).hexdigest() == result[0] if result else False

    def get_user(self, username: str) -> Optional[dict]:
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT id, username, role, department_id 
            FROM users 
            WHERE username=?
        ''', (username,))
        result = cursor.fetchone()
        if result:
            return {
                "id": result[0],
                "username": result[1],
                "role": result[2],
                "department_id": result[3]
            }
        return None

if __name__ == '__main__':
    db = DBHelper()
    db.create_user('admin', 'pds@123456', 'admin')
    print('Database initialized with admin user.')
