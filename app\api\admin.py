from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.user import User
from app.models.department import Department
from app.models.appointment import Appointment
from app.models.log import Log
from app.database import db
from app.utils.encryption import encryptor
from app.utils.error_handlers import APIError
from app.middleware.auth import admin_required
from datetime import datetime, timedelta
from sqlalchemy import func

bp = Blueprint('admin', __name__, url_prefix='/admin')

@bp.route('/users', methods=['GET'])
@jwt_required()
@admin_required
def list_users():
    department_id = request.args.get('department_id', type=int)
    role = request.args.get('role')
    
    query = User.query
    if department_id:
        query = query.filter_by(department_id=department_id)
    if role:
        query = query.filter_by(role=role)
    
    users = query.all()
    return jsonify([user.to_dict() for user in users])

@bp.route('/users', methods=['POST'])
@jwt_required()
@admin_required
def create_user():
    data = request.get_json()
    
    if User.query.filter_by(username=data['username']).first():
        raise APIError("Username already exists", 400)
    
    user = User(
        username=data['username'],
        role=data['role'],
        department_id=data.get('department_id'),
        contact=data.get('contact')
    )
    user.set_password(data['password'])
    
    db.session.add(user)
    db.session.commit()
    
    # 记录操作日志
    log = Log(
        user_id=get_jwt_identity(),
        action='create',
        target_type='user',
        target_id=user.id,
        details={'username': user.username, 'role': user.role}
    )
    db.session.add(log)
    db.session.commit()
    
    return jsonify(user.to_dict())

@bp.route('/departments', methods=['GET', 'POST'])
@jwt_required()
@admin_required
def manage_departments():
    if request.method == 'GET':
        departments = Department.query.all()
        return jsonify([dept.to_dict() for dept in departments])
    
    data = request.get_json()
    department = Department(
        name=data['name'],
        code=data['code']
    )
    db.session.add(department)
    db.session.commit()
    
    return jsonify(department.to_dict())

@bp.route('/logs', methods=['GET'])
@jwt_required()
@admin_required
def get_logs():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    action = request.args.get('action')
    
    query = Log.query
    if start_date:
        query = query.filter(Log.created_at >= datetime.fromisoformat(start_date))
    if end_date:
        query = query.filter(Log.created_at < datetime.fromisoformat(end_date))
    if action:
        query = query.filter_by(action=action)
    
    logs = query.order_by(Log.created_at.desc()).all()
    return jsonify([log.to_dict() for log in logs])

@bp.route('/statistics', methods=['GET'])
@jwt_required()
@admin_required
def get_statistics():
    period = request.args.get('period', 'day')  # day/week/month
    end_date = datetime.now()
    
    if period == 'day':
        start_date = end_date - timedelta(days=30)
        group_by = func.date(Appointment.created_at)
    elif period == 'week':
        start_date = end_date - timedelta(weeks=12)
        group_by = func.date_trunc('week', Appointment.created_at)
    else:  # month
        start_date = end_date - timedelta(days=365)
        group_by = func.date_trunc('month', Appointment.created_at)
    
    # 访客量统计
    visitor_stats = db.session.query(
        group_by.label('date'),
        func.count(Appointment.id).label('count')
    ).filter(
        Appointment.created_at.between(start_date, end_date)
    ).group_by(group_by).all()
    
    # 审批通过率统计
    approval_stats = db.session.query(
        group_by.label('date'),
        func.count(Appointment.id).label('total'),
        func.sum(case([(Appointment.status == AppointmentStatus.APPROVED, 1)], else_=0)).label('approved')
    ).filter(
        Appointment.created_at.between(start_date, end_date)
    ).group_by(group_by).all()
    
    return jsonify({
        'visitor_stats': [{'date': str(date), 'count': count} for date, count in visitor_stats],
        'approval_stats': [{
            'date': str(date),
            'total': total,
            'approved': approved,
            'rate': round(approved/total*100 if total > 0 else 0, 2)
        } for date, total, approved in approval_stats]
    }) 