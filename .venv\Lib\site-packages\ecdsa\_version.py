
# This file was generated by 'versioneer.py' (0.21) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-04-08T20:59:55+0200",
 "dirty": false,
 "error": null,
 "full-revisionid": "be70016f8911f79e891a65dcfcb602e5ba866ed3",
 "version": "0.19.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
